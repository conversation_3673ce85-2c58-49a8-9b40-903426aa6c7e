"use client";
import { motion } from "framer-motion";

import Image from "next/image";
import { type TimelineItem } from "~/constants/gallery";
import { PhotoProvider, PhotoView } from "react-photo-view";
import "react-photo-view/dist/react-photo-view.css";
import <PERSON>rip<PERSON> from "next/script";

interface Group {
  category: string;
  items: TimelineItem[];
}

const groupItemsByCategory = (items: TimelineItem[]): Group[] => {
  const groups: Record<string, TimelineItem[]> = {};

  items.forEach((item) => {
    const category = item.category;
    groups[category] = groups[category] ?? [];
    groups[category].push(item);
  });

  return Object.entries(groups)
    .map(([category, items]) => ({
      category,
      items: items.sort(
        (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
      ),
    }))
    .sort(
      (a, b) =>
        new Date(b.items[0]?.date ?? 0).getTime() -
        new Date(a.items[0]?.date ?? 0).getTime(),
    );
};

interface TimelineProps {
  items: TimelineItem[];
}

export const Timeline: React.FC<TimelineProps> = ({ items }) => {
  const groupedItems = groupItemsByCategory(items);

  // Generate JSON-LD structured data
  const jsonLd = {
    "@context": "https://schema.org",
    "@type": "ItemList",
    itemListElement: groupedItems.map((group, groupIndex) => ({
      "@type": "ListItem",
      position: groupIndex + 1,
      item: {
        "@type": "ItemList",
        name: group.category,
        itemListElement: group.items.map((item, itemIndex) => ({
          "@type": "ListItem",
          position: itemIndex + 1,
          item: {
            "@type": "ImageObject",
            name: item.title,
            description: item.description,
            datePublished: item.date,
            image: item.image,
          },
        })),
      },
    })),
  };

  return (
    <section className="py-8">
      {/* Add JSON-LD script */}
      <Script
        id="timeline-json-ld"
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      <PhotoProvider
        speed={() => 400}
        easing={(type) =>
          type === 2
            ? "cubic-bezier(0.36, 0, 0.66, -0.56)"
            : "cubic-bezier(0.34, 1.56, 0.64, 1)"
        }
        maskOpacity={0.5}
        bannerVisible={false}
      >
        {groupedItems.map((group, i) => (
          <div key={group.category} className="mb-12">
            {/* Category Heading */}
            <h2 className="mb-6 text-2xl font-bold text-gray-900 md:text-3xl">
              {group.category}
            </h2>

            {/* Grouped Items */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
              {group.items.map((item) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  viewport={{ once: true, margin: "0px 0px -15% 0px" }}
                  className="flex flex-col rounded-lg border shadow-sm"
                >
                  {/* Image (Top) */}
                  {item.image && (
                    <div className="relative w-full">
                      <PhotoView src={item.image}>
                        <Image
                          src={item.image}
                          alt={item.title}
                          className="rounded-t-lg object-cover"
                          width={800}
                          height={500}
                          priority={i < 1 ? true : false}
                          loading={i < 1 ? "eager" : "lazy"}
                        />
                      </PhotoView>
                    </div>
                  )}
                  {/* Content (Bottom) */}
                  <div className="flex-1 p-4">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {item.title}
                    </h3>
                    <p className="mt-2 text-gray-600">{item.description}</p>
                    <time className="mt-2 block text-sm text-gray-500">
                      {item.date}
                    </time>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        ))}
      </PhotoProvider>
    </section>
  );
};
