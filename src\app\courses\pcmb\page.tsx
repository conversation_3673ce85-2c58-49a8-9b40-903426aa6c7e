import type { Metadata } from "next";
import { CoursesPageHero } from "~/components/courses-components/courses-page-hero/courses-page-hero";
import { ProgramFeatureSection } from "~/components/courses-components/feature-cards-section";
import { TrainingProcessSection } from "~/components/courses-components/training-process-section";
import { WhyChooseUs } from "~/components/courses-components/why-choose-us";
import { pcmbProgramFeatureList } from "~/constants/courses";

export const metadata: Metadata = {
  title: "PCMB Courses in Bangalore | Expert Coaching at Aims Academy",
  description:
    "Enroll in Aims Academy's PCMB courses in Bangalore. Expert faculty, comprehensive syllabus coverage, and personalized mentoring for top results.",
  keywords: [
    "PCMB Courses Bangalore",
    "PCMB Coaching Yelahanka",
    "Best PCMB Classes in Bangalore",
    "PCMB Integrated Courses",
    "PCMB Preparation Bangalore",
  ],
  openGraph: {
    title: "PCMB Courses in Bangalore | Expert Coaching at Aims Academy",
    description:
      "Join Aims Academy for top-notch PCMB coaching in Bangalore. Expert guidance, regular assessments, and proven success. Enroll now!",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Aims Academy PCMB Courses",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "PCMB Courses in Bangalore | Expert Coaching at Aims Academy",
    description:
      "Achieve excellence in PCMB with Aims Academy's expert coaching in Bangalore. Comprehensive preparation and personalized mentoring.",
  },
};

const currentYear = new Date().getFullYear();

const heroProps = {
  heading: "Best PCMB Courses for NEET & JEE Aspirants",
  subheading: `Comprehensive Science Program ${currentYear}`,
  description:
    "Master Physics, Chemistry, Mathematics, Biology with expert faculty, personalized mentoring, and competitive exam preparation.",
  image: { src: "/exam_courses_1.svg", alt: "PCMB Course Structure" },
  buttons: {
    primary: { text: "Enroll Now", url: "#contact-form" },
    secondary: { text: "learn more", url: "#contact-form" },
  },
};

const PcmbPage = () => (
  <article>
    <CoursesPageHero {...heroProps} />
    <ProgramFeatureSection
      features={pcmbProgramFeatureList}
      imageSrc="/program-feature-img.png"
    />
    <WhyChooseUs />
    <TrainingProcessSection courseType="PCMB" />
  </article>
);

export default PcmbPage;
