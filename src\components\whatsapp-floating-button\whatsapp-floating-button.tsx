"use client";
import { But<PERSON> } from "~/components/ui/button";
import WhatsappIcon from "~/icons/whatsapp";
import { sendGAEvent } from "@next/third-parties/google"; // Import sendGAEvent

export default function WhatsAppButton() {
  const handleClick = () => {
    sendGAEvent("event", "whatsapp_button_click", {
      category: "User Interaction",
      action: "Clicked WhatsApp Button",
      label: "WhatsApp Support",
      value: window.location.href,
      timestamp: new Date().toISOString(),
    });

    window.open(
      `https://wa.me/+919008266404?text=${encodeURIComponent("Hi AIMS Academy, I have a question.")}`,
      "_blank",
    );
  };

  return (
    <Button
      onClick={handleClick}
      variant={"secondary"}
      className="fixed bottom-6 right-2 z-50 flex items-center gap-2 rounded-2xl border-2 border-white bg-green-800 px-4 py-6 font-semibold text-white shadow-lg hover:bg-green-900"
      aria-label="Chat with us on WhatsApp"
    >
      <WhatsappIcon style={{ width: "1.5rem", height: "1.5rem" }} />
      <p className="text-base font-bold">Contact 24/7</p>
    </Button>
  );
}
