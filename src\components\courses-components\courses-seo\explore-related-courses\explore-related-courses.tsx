"use client";

import Link from "next/link";
import { getFilteredCoursesList } from "~/utils/course-list-utils/get-filtered-course";
import { motion } from "framer-motion";

export interface RelatedCourseListProps {
  title?: string;
  excludeCourses: string;
}

export const RelatedCourseList = ({
  title = "Explore Related Courses",
  excludeCourses,
}: RelatedCourseListProps) => (
  <motion.div initial={{ opacity: 0 }} whileInView={{ opacity: 1 }}>
    <section className="py-16">
      <h3 className="mb-4 text-xl font-bold text-gray-900 dark:text-white">
        {title}
      </h3>
      <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
        {getFilteredCoursesList(excludeCourses).map((course) => (
          <Link
            key={course}
            href={`/courses/${course.toLowerCase().replace(/ /g, "-")}`}
            className="transform rounded-lg border border-gray-200 bg-white p-2 text-center shadow-sm transition-all hover:scale-105 hover:border-accent hover:bg-accent/10 hover:shadow-lg dark:border-gray-700 dark:bg-gray-800 dark:hover:bg-accent/20"
          >
            <span className="text-gray-800 dark:text-gray-200">{course}</span>
          </Link>
        ))}
      </div>
    </section>
  </motion.div>
);
