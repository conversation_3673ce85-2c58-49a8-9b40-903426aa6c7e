"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "~/components/ui/dialog";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { useContactUsForm } from "~/hooks/use-contact-us-form";
import { ContactUsFormFields } from "../contact-us-form/contact-us-form-fields";
import { useEffect, useState, useCallback } from "react";
import { LOADER_LOADING_STATES } from "~/constants/constants";
import { MultiStepLoader as Loader } from "../../ui/multi-step-loader";
import useLocalStorage from "use-local-storage";

// Define the type for the popup data stored in localStorage
interface PopupData {
  date: string; // Current date in "MM/DD/YYYY" format
  slot: "AM" | "PM"; // Current time slot (AM or PM)
  count: number; // Number of times the popup has been shown in the current slot
  submitted: boolean; // Whether the form has been successfully submitted
}

export function ContactUsPopup() {
  const { formData, handleFormChange, handleSubmit, loading, submitStatus } =
    useContactUsForm(undefined, "popup-contact-form");
  const [isOpen, setIsOpen] = useState<boolean>(false);

  // Function to get the current time slot (AM or PM)
  const getCurrentSlot = useCallback((): "AM" | "PM" => {
    const hours = new Date().getHours();
    return hours < 12 ? "AM" : "PM";
  }, []);

  // Initialize popup data with useLocalStorage hook
  const [popupData, setPopupData] = useLocalStorage<PopupData>("popupData", {
    date: new Date().toLocaleDateString(),
    slot: getCurrentSlot(),
    count: 0,
    submitted: false,
  });

  // Function to check if the popup should be shown, wrapped in useCallback
  const shouldShowPopup = useCallback(
    (popupData: PopupData): boolean => {
      const today = new Date().toLocaleDateString();
      const currentSlot = getCurrentSlot();

      // If the form has already been submitted, don't show the popup
      if (popupData.submitted) {
        return false;
      }

      // If the date or slot has changed, reset the count
      if (popupData.date !== today || popupData.slot !== currentSlot) {
        return true;
      }

      // Show the popup only if it hasn't been shown in the current slot
      return popupData.count < 1;
    },
    [getCurrentSlot],
  );

  useEffect(() => {
    const today = new Date().toLocaleDateString();
    const currentSlot = getCurrentSlot();

    // Check if the popup should be shown
    if (shouldShowPopup(popupData)) {
      const delay = 5000; // 5 seconds delay
      const timeoutId = setTimeout(() => {
        setIsOpen(true);

        // Update the popup data using the hook
        setPopupData({
          date: today,
          slot: currentSlot,
          count:
            popupData.date === today && popupData.slot === currentSlot
              ? popupData.count + 1
              : 1,
          submitted: popupData.submitted,
        });
      }, delay);

      return () => clearTimeout(timeoutId);
    }
  }, [popupData, setPopupData, shouldShowPopup]);

  // Handle form submission
  const handleFormSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    setIsOpen(false);
    await handleSubmit(event);
  };

  // Close the modal on successful submission
  useEffect(() => {
    if (submitStatus === "success") {
      setIsOpen(false);

      // Mark the form as submitted using the hook
      setPopupData({
        ...popupData,
        submitted: true, // Mark the form as submitted
      });
    } else if (submitStatus === "error") {
      // Keep the modal open if submission fails
      setIsOpen(true);
    }
  }, [submitStatus, popupData, setPopupData]);

  return (
    <>
      <Loader loadingStates={LOADER_LOADING_STATES} loading={loading} />
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Contact Us</DialogTitle>
            <DialogDescription>
              We&apos;d love to hear from you! Please fill out the form below.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleFormSubmit} className="space-y-4">
            <ContactUsFormFields
              formData={formData}
              onFormChange={handleFormChange}
              formId="popup"
            />
            <Button type="submit" className="w-full">
              Submit
            </Button>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
}
