import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Terms and Conditions",
  description:
    "Read our terms and conditions for using Aims Academy's website. Learn about cookies, licenses, hyperlinking, content liability, privacy, and disclaimer.",
};

const TermsAndConditions = () => {
  return (
    <article>
      <h1 className="mb-4 text-3xl font-bold">Terms and Conditions</h1>

      <p className="mb-4">
        These terms and conditions outline the rules and regulations for the use
        of Aims Academy&apos;s Website, located at aimsacademy.in.
      </p>

      <p className="mb-4">
        By accessing this website we assume you accept these terms and
        conditions. Do not continue to use aimsacademy.in if you do not agree to
        take all of the terms and conditions stated on this page.
      </p>

      <section className="mb-8">
        <h3 className="mb-2 text-2xl font-semibold">Cookies</h3>
        <p className="mb-4">
          We employ the use of cookies. By accessing aimsacademy.in, you agreed
          to use cookies in agreement with the Aims Academy&apos;s Privacy
          Policy.
        </p>
        <p className="mb-4">
          Most interactive websites use cookies to let us retrieve the user’s
          details for each visit. Cookies are used by our website to enable the
          functionality of certain areas to make it easier for people visiting
          our website.
        </p>
      </section>

      <section className="mb-8">
        <h3 className="mb-2 text-2xl font-semibold">License</h3>
        <p className="mb-4">
          Unless otherwise stated, Aims Academy and/or its licensors own the
          intellectual property rights for all material on aimsacademy.in. All
          intellectual property rights are reserved. You may access this from
          aimsacademy.in for your own personal use subjected to restrictions set
          in these terms and conditions.
        </p>
        <ul className="list-disc pl-6">
          <li>Republish material from aimsacademy.in</li>
          <li>Sell, rent or sub-license material from aimsacademy.in</li>
          <li>Reproduce, duplicate or copy material from aimsacademy.in</li>
          <li>Redistribute content from aimsacademy.in</li>
        </ul>
      </section>

      <section className="mb-8">
        <h3 className="mb-2 text-2xl font-semibold">
          Hyperlinking to our Content
        </h3>
        <p className="mb-4">
          The following organizations may link to our Website without prior
          written approval:
        </p>
        <ul className="list-disc pl-6">
          <li>Government agencies</li>
          <li>Search engines</li>
          <li>News organizations</li>
          <li>Online directory distributors</li>
        </ul>
      </section>

      <section className="mb-8">
        <h3 className="mb-2 text-2xl font-semibold">iFrames</h3>
        <p className="mb-4">
          Without prior approval and written permission, you may not create
          frames around our Webpages that alter in any way the visual
          presentation or appearance of our Website.
        </p>
      </section>

      <section className="mb-8">
        <h3 className="mb-2 text-2xl font-semibold">Content Liability</h3>
        <p className="mb-4">
          We shall not be hold responsible for any content that appears on your
          Website. You agree to protect and defend us against all claims that is
          rising on your Website.
        </p>
      </section>

      <section className="mb-8">
        <h3 className="mb-2 text-2xl font-semibold">Your Privacy</h3>
        <p className="mb-4">Please read our Privacy Policy.</p>
      </section>

      <section className="mb-8">
        <h3 className="mb-2 text-2xl font-semibold">Disclaimer</h3>
        <p className="mb-4">
          To the maximum extent permitted by applicable law, we exclude all
          representations, warranties and conditions relating to our website and
          the use of this website.
        </p>
      </section>
    </article>
  );
};

export default TermsAndConditions;
