import { ArrowUpRight } from "lucide-react";
import Link from "next/link";
import ContactUsForm from "~/components/contact-components/contact-us-form/contact-us-form";

import { Button } from "~/components/ui/button";

export interface CoursesPageHeroProps {
  heading?: string;
  subheading?: string;
  description?: string;
  image?: {
    src: string;
    alt: string;
  };
  buttons?: {
    primary?: {
      text: string;
      url: string;
    };
    secondary?: {
      text: string;
      url: string;
    };
  };
}

const CoursesPageHero = ({
  heading = "AIMS Academy",
  subheading = " Excellence in Education",
  description = "Join AIMS Academy to experience top-notch education and skill development. Our courses are designed to help you achieve your career goals.",
  buttons = {
    primary: {
      text: "Enroll Now",
      url: "#/contact-form",
    },
    secondary: {
      text: "Learn More",
      url: "#/contact-form",
    },
  },
  image = {
    src: "/exam_courses_1.svg",
    alt: "AIMS Academy",
  },
}: CoursesPageHeroProps) => {
  return (
    <section className="bg-background py-2">
      <div className="flex flex-col items-baseline gap-10 lg:my-0 lg:flex-row">
        <div className="flex flex-col gap-7 lg:w-2/3">
          <div className="text-4xl font-semibold text-foreground md:text-3xl lg:text-5xl">
            <h1>{heading}</h1>
            <p className="text-muted-foreground">{subheading}</p>
          </div>
          <p className="text-base text-muted-foreground md:text-lg lg:text-xl">
            {description}
          </p>
          <div className="flex flex-wrap items-start gap-5 lg:gap-7">
            <Button asChild>
              <Link href={buttons.primary?.url ?? "#"}>
                <div className="flex items-center gap-2">
                  <ArrowUpRight className="size-4" />
                </div>
                <span className="whitespace-nowrap pl-4 pr-6 text-sm lg:pl-6 lg:pr-8 lg:text-base">
                  {buttons.primary?.text}
                </span>
              </Link>
            </Button>
            <Button asChild variant="link" className="underline">
              <Link href={buttons.secondary?.url ?? "#"}>
                {buttons.secondary?.text}
              </Link>
            </Button>
          </div>
        </div>
        <div className="relative z-10">
          <ContactUsForm formId="courses-hero" />
        </div>
      </div>
    </section>
  );
};

export { CoursesPageHero };
