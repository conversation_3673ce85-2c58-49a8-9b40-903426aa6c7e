"use client";
import Image from "next/image";
import Script from "next/script";
import ColourfulText from "~/components/ui/colourful-text";
import { SITE_DOMAIN } from "~/constants/siteConfig";

interface TeamMember {
  id: string;
  name: string;
  role: string;
  position: string;
  experience: string;
  avatar: string;
}

interface Team1Props {
  heading?: string;
  description?: string;
  members?: TeamMember[];
}

const OurFaculty = ({
  heading = "Meet our Expert Teaching Faculty",
  description = "AIMS Academy stands as a beacon of excellence, providing students with the resources, guidance, and support needed to conquer both board exams and competitive tests like JEE, NEET and KCET. With a visionary approach and a commitment to academic excellence, AIMS Academy continues to transform the lives of countless aspirants.",
  members = [
    {
      id: "person-1",
      name: "<PERSON><PERSON>",
      position: "Founder Director",
      role: "Subject expert in Chemistry",
      experience: "20+ years",
      avatar: "/faculty/aims_academy_Krishna_Reddy.jpg",
    },
    {
      id: "person-2",
      name: "<PERSON><PERSON>",
      position: "Founder Director",
      role: "Subject expert in Chemistry",
      experience: "18+ years",
      avatar: "/faculty/aims_academy_Venkatesh.jpg",
    },
    {
      id: "person-3",
      name: "<PERSON><PERSON>",
      position: "Founder Director",
      role: "Subject expert in Zoology",
      experience: "18+ years",
      avatar: "/faculty/aims_academy_Kranthi_Kiran_Kumar.jpg",
    },
    {
      id: "person-4",
      name: "Dr. P. Ramana",
      role: "Subject expert in Botany",
      experience: "20+ years",
      position: "Founder Director",
      avatar: "/faculty/aims_academy_Ramana.jpg",
    },
    {
      id: "person-5",
      name: "K. Srinivasa Rao",
      role: "Subject expert in Physics",
      experience: "30+ years",
      position: "Founder Director",
      avatar: "/faculty/aims_academy_Srinivasa_Rao.jpg",
    },
    {
      id: "person-6",
      name: "M. Nagaraju",
      role: "Subject expert in Physics",
      experience: "20+ years",
      position: "Founder Director",
      avatar: "/faculty/aims_academy_Nagaraju.jpg",
    },
    {
      id: "person-7",
      name: "G V S Appalaraju",
      role: "Subject expert in Physics",
      experience: "30+ years",
      position: "Founder Director",
      avatar: "/faculty/aims_academy_Appalaraju.jpg",
    },
  ],
}: Team1Props) => {
  // Generate structured data for SEO
  const facultyStructuredData = {
    "@context": "https://schema.org",
    "@type": "EducationalOrganization",
    name: "Aims Academy Bangalore",
    description:
      "Aims Academy Bangalore offers the best PU and NEET coaching with expert faculty and a proven curriculum.",
    employee: members.map((member) => ({
      "@type": "Person",
      name: member.name,
      jobTitle: member.position,
      description: `${member.role} with ${member.experience} of experience.`,
      image: `${SITE_DOMAIN}${member.avatar}`,
    })),
  };

  return (
    <section className="py-8">
      {/* Structured Data for SEO */}
      <Script
        id="faculty-schema"
        type="application/ld+json"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(facultyStructuredData),
        }}
      />

      {/* Heading */}
      <div className="flex flex-col items-center text-center">
        <h2 className="my-6 text-pretty text-2xl font-bold lg:text-4xl">
          <ColourfulText text={heading} />
        </h2>
        <p className="mb-8 max-w-3xl text-muted-foreground lg:text-xl">
          {description}
        </p>
      </div>

      {/* Faculty Members Grid */}
      <div className="mt-16 grid gap-x-8 gap-y-16 md:grid-cols-2 lg:grid-cols-4">
        {members.map((person) => (
          <div key={person.id} className="flex flex-col items-center">
            {/* Optimized Image */}
            <Image
              className="rounded-3xl object-cover"
              src={person.avatar}
              alt={`${person.name}, ${person.position} at Aims Academy`} // Improved alt text
              width={256}
              height={256}
              loading="lazy"
              priority={false}
            />
            <p className="mt-4 text-center font-medium">{person.name}</p>
            <p className="text-center text-muted-foreground">
              {person.position}
            </p>
            <p className="text-center italic text-muted-foreground">
              {person.role}
            </p>
            <p className="text-center text-muted-foreground">
              {person.experience}
            </p>
          </div>
        ))}
      </div>
    </section>
  );
};

export { OurFaculty };
