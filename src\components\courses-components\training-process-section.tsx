"use client";

import {
  <PERSON><PERSON><PERSON><PERSON>ist,
  <PERSON>bul<PERSON>,
  <PERSON><PERSON><PERSON>,
  Clock,
  MessageSquare,
  Refresh<PERSON>w,
  Lapt<PERSON>,
} from "lucide-react";
import { Tilt } from "react-tilt";
import { motion } from "framer-motion";
import {
  TRAINING_PROCESS,
  DEFAULT_TRAINING_PROCESS,
} from "~/constants/training-process";

interface TrainingProcessSectionProps {
  courseType: keyof typeof TRAINING_PROCESS;
}

export function TrainingProcessSection({
  courseType,
}: TrainingProcessSectionProps) {
  const process = TRAINING_PROCESS[courseType] ?? DEFAULT_TRAINING_PROCESS;
  const { description, steps } = process;

  const iconMap = {
    "Initial Assessment": ClipboardList,
    "Concept Building": Lightbulb,
    "Practice & Revision": Pencil,
    "Mock Tests": Clock,
    "Personalized Mentoring": MessageSquare,
    "Continuous Evaluation": RefreshCw,
    "Technology Integration": Laptop,
    "Problem Solving": Pencil,
  };

  return (
    <section id="program-details" className="mx-auto py-12">
      <div className="mx-auto max-w-3xl text-center">
        <h2 className="mb-4 text-3xl font-bold">Our Training Process</h2>
        <p className="mb-8 text-lg text-muted-foreground">{description}</p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {steps.map((step, i) => {
          const IconComponent =
            iconMap[step.title as keyof typeof iconMap] || ClipboardList;
          return (
            <Tilt key={i} options={{ max: 15, scale: 1.05, speed: 300 }}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ delay: i * 0.1 }}
                viewport={{ once: true }}
                className="h-full"
              >
                <div
                  className={`h-full rounded-xl bg-gradient-to-br ${step.gradient} p-0.5`}
                >
                  <div className="flex h-full flex-col justify-between rounded-xl bg-background p-6">
                    <div>
                      <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-white/10">
                        <IconComponent className="h-8 w-8" />
                      </div>
                      <h3 className="mb-2 text-xl font-semibold">
                        {step.title}
                      </h3>
                      <div className="space-y-2">
                        <p>{step.description}</p>
                        {step.subDescription && (
                          <p className="text-sm font-medium">
                            {step.subDescription}
                          </p>
                        )}
                        {step.note && (
                          <p className="text-xs text-muted-foreground">
                            {step.note}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </Tilt>
          );
        })}
      </div>
    </section>
  );
}
