"use client";

import { useEffect, useRef, useState, useMemo } from "react";

export function useInView(
  options?: IntersectionObserverInit,
): [React.RefObject<HTMLDivElement>, boolean] {
  const ref = useRef<HTMLDivElement>(null);
  const [inView, setInView] = useState(false);

  const memoizedOptions = useMemo(() => options, [options]);

  useEffect(() => {
    if (typeof window === "undefined") return;
    const node = ref.current;
    if (!node) return;
    const observer = new window.IntersectionObserver((entries) => {
      const entry = entries[0];
      setInView(!!entry && entry.isIntersecting);
    }, memoizedOptions);
    observer.observe(node);
    return () => observer.disconnect();
  }, [ref, memoizedOptions]);

  return [ref, inView];
}
