interface FaqItem {
  question: string;
  answer: string;
}
export type FaqSchema = {
  "@context": string;
  "@type": "FAQPage";
  mainEntity: {
    "@type": "Question";
    name: string;
    acceptedAnswer: {
      "@type": "Answer";
      text: string;
    };
  }[];
};

export const generateFaqSchema = (faqItems: FaqItem[]): FaqSchema => ({
  "@context": "https://schema.org",
  "@type": "FAQPage",
  mainEntity: faqItems?.map((faq) => ({
    "@type": "Question",
    name: faq.question,
    acceptedAnswer: { "@type": "Answer", text: faq.answer },
  })),
});
