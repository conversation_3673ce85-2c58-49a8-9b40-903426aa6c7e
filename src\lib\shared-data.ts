import { cache } from "react";
import { fetchGoogleReviews } from "./featurable/featurable";
import type { ReviewRoot } from "./featurable/types";

/**
 * Cached version of fetchGoogleReviews to ensure we only call the API once
 * across the entire application, even during server-side rendering
 */
export const getGoogleReviews = cache(async (): Promise<ReviewRoot> => {
  const cacheCallTime = new Date().toISOString();
  console.log(
    `🔄 Fetching Google reviews (cached) - Cache call time: ${cacheCallTime}`,
  );
  const startTime = Date.now();
  const result = await fetchGoogleReviews();
  const endTime = Date.now();
  console.log(
    `✅ Google reviews fetch completed - Total time: ${endTime - startTime}ms - Cache call time: ${cacheCallTime}`,
  );
  return result;
});
