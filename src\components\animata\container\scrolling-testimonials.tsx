import {
  ReviewCard,
  type ReviewCardProps,
} from "~/components/react-google-reviews/review-card";
import Marquee from "./marquee";

interface TestimonialProps {
  data: ReviewCardProps[];
}

function TestimonialCard({
  testimonial: { avatarUrl, name, comment, rating, date },
}: {
  testimonial: ReviewCardProps;
}) {
  return (
    <ReviewCard
      name={name}
      date={date}
      rating={rating}
      comment={comment}
      avatarUrl={avatarUrl}
    />
  );
}

export default function ScrollingTestimonials({ data }: TestimonialProps) {
  return (
    <div className="w-full overflow-hidden">
      <Marquee className="[--duration:100s]" pauseOnHover applyMask={false}>
        {data.map((testimonial) => (
          <TestimonialCard key={testimonial.name} testimonial={testimonial} />
        ))}
      </Marquee>

      <Marquee
        reverse
        className="[--duration:100s]"
        pauseOnHover
        applyMask={false}
      >
        {data.reverse().map((testimonial) => (
          <TestimonialCard key={testimonial.name} testimonial={testimonial} />
        ))}
      </Marquee>
    </div>
  );
}
