"use client";
import { motion } from "framer-motion";
import {
  ChartBar,
  Lightbulb,
  MessageSquare,
  Smile,
  Users,
  Home,
  Leaf,
  User,
} from "lucide-react";

interface Reason {
  title: string;
  description: string;
  icon: JSX.Element;
}

interface Feature43Props {
  heading?: string;
  reasons?: Reason[];
}

const OurStrikingTraits = ({
  heading = "Our Striking Traits of Curriculum",
  reasons = [
    {
      title: "Expert Teaching Faculty",
      description:
        "Highly experienced and dedicated educators committed to delivering quality education tailored to each student's needs.",
      icon: <Users className="size-6" />,
    },
    {
      title: "Periodic Assessment and Analysis",
      description:
        "Regular tests and performance reviews to ensure students stay on track and excel in competitive exams.",
      icon: <ChartBar className="size-6" />,
    },
    {
      title: "Genius Guidance for Holistic Development",
      description:
        "Structured mentoring programs designed to foster both academic and personal growth.",
      icon: <Lightbulb className="size-6" />,
    },
    {
      title: "Counsel to Excel Strategy for Doubt Clarification",
      description:
        "Effective strategies and one-on-one sessions to resolve doubts and strengthen understanding.",
      icon: <MessageSquare className="size-6" />,
    },
    {
      title: "Empathetic Approach to Address Learning Difficulties",
      description:
        "A compassionate and supportive learning environment that addresses individual challenges.",
      icon: <Smile className="size-6" />,
    },
    {
      title: "Individual Care",
      description:
        "Personalized attention to each student, nurturing their strengths and improving weaknesses.",
      icon: <User className="size-6" />,
    },
    {
      title: "Home-Like Hostel and Food Provisions",
      description:
        "Comfortable accommodation and nutritious meals that create a homely atmosphere for students.",
      icon: <Home className="size-6" />,
    },
    {
      title: "Splendid Conducive Environment",
      description:
        "A serene and positive learning environment that promotes focus and academic excellence.",
      icon: <Leaf className="size-6" />,
    },
  ],
}: Feature43Props) => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <section className="px-8 py-16">
      <div className="mb-10 md:mb-20">
        <h2 className="mb-2 text-center text-3xl font-semibold lg:text-5xl">
          {heading}
        </h2>
      </div>
      <motion.div
        className="grid gap-10 md:grid-cols-2 lg:grid-cols-3"
        variants={containerVariants}
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true, amount: 0.2 }}
      >
        {reasons.map((reason, i) => (
          <motion.div key={i} className="flex flex-col" variants={itemVariants}>
            <div className="mb-5 flex size-16 items-center justify-center rounded-full bg-accent">
              {reason.icon}
            </div>
            <h3 className="mb-2 text-xl font-semibold">{reason.title}</h3>
            <p className="text-muted-foreground">{reason.description}</p>
          </motion.div>
        ))}
      </motion.div>
    </section>
  );
};

export { OurStrikingTraits };
