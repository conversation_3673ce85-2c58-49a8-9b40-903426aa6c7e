import "~/styles/globals.css";

import { Geist<PERSON><PERSON> } from "geist/font/sans";
import { type Viewport, type Metadata } from "next";
import Script from "next/script";
import { NavMenuWrapper } from "~/components/nav-menu/nav-menu-wrapper";
import { ThemeProvider } from "~/components/theme-provider/theme-provider";
import { Footer } from "~/components/footer/footer";
import NextTopLoader from "nextjs-toploader";
import { Toaster } from "~/components/ui/toaster";
import { SITE_DOMAIN } from "~/constants/siteConfig";
import { GoogleAnalytics, GoogleTagManager } from "@next/third-parties/google";
import WhatsAppButton from "~/components/whatsapp-floating-button/whatsapp-floating-button";
import { DynamicBreadcrumb } from "~/components/dynamic-breadcrumb/DynamicBreadcrumb";
import {
  generateEducationalOrgSchema,
  generateLocalBusinessSchema,
  generateWebsiteSchema,
} from "~/lib/structured-data";

export const metadata: Metadata = {
  title: {
    template: "%s | Aims Academy Bangalore",
    default:
      "Aims Academy | Top NEET Long Term Coaching & PU Integrated Coaching Center in Bangalore",
  },
  description:
    "Aims Academy –  Top NEET Long Term Coaching & PU Integrated Coaching Center in Bangalore. Expert faculty, personalized mentoring & proven strategies for NEET, JEE & KCET. Enroll now!",
  keywords: [
    "BEST NEET Coaching Bangalore",
    "PUC Coaching Bangalore",
    "PCMB Courses Yelahanka",
    "Best IIT & JEE Coaching in Academy North Bangalore",
    "PU College Near RTO Office",
    "Karnataka PUC Top Rankers",
  ],
  metadataBase: new URL(SITE_DOMAIN),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: "Aims Academy | Top NEET & PU College in Bangalore",
    description:
      "Aims Academy –  Top NEET Long Term Coaching & PU Integrated Coaching Center in Bangalore",
    url: SITE_DOMAIN,
    siteName: "Aims Academy",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Aims Academy Campus in Yelahanka",
      },
    ],
    locale: "en_IN",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Aims Academy | Bangalore's Best PUC Coaching",
    description:
      "Ranked #1 NEET & PU College in Yelahanka. 9008466404 | RTO Office Road",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  viewportFit: "cover",
};

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html
      lang="en"
      className={`${GeistSans.variable}`}
      suppressHydrationWarning
    >
      <head>
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
      </head>
      <body>
        <NextTopLoader showSpinner={false} color="#023CB6" />
        <ThemeProvider attribute="class" enableSystem>
          <NavMenuWrapper />
          <main className="p-4">
            <DynamicBreadcrumb />
            {children}
          </main>
          <Footer />
          <Toaster />
          <WhatsAppButton />
        </ThemeProvider>
        <GoogleAnalytics gaId="G-SS85E0SRMD" />
        <GoogleTagManager gtmId="GTM-WTGKZGGT" />

        {/* Structured data for SEO - loaded after page is interactive */}
        <Script
          id="organization-schema"
          type="application/ld+json"
          strategy="afterInteractive"
        >
          {JSON.stringify(generateEducationalOrgSchema())}
        </Script>

        {/* LocalBusiness schema for SEO */}
        <Script
          id="local-business-schema"
          type="application/ld+json"
          strategy="afterInteractive"
        >
          {JSON.stringify(generateLocalBusinessSchema())}
        </Script>

        {/* Website schema for SEO */}
        <Script
          id="website-schema"
          type="application/ld+json"
          strategy="afterInteractive"
        >
          {JSON.stringify(generateWebsiteSchema())}
        </Script>
      </body>
    </html>
  );
}
