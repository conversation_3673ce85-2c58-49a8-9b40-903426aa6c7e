// CSS will be loaded asynchronously to prevent render blocking

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "geist/font/sans";
import { type Viewport, type Metadata } from "next";
import <PERSON>ript from "next/script";
import { NavMenuWrapper } from "~/components/nav-menu/nav-menu-wrapper";
import { ThemeProvider } from "~/components/theme-provider/theme-provider";
import { Footer } from "~/components/footer/footer";
import NextTopLoader from "nextjs-toploader";
import { Toaster } from "~/components/ui/toaster";
import { SITE_DOMAIN } from "~/constants/siteConfig";
import { GoogleAnalytics, GoogleTagManager } from "@next/third-parties/google";
import WhatsAppButton from "~/components/whatsapp-floating-button/whatsapp-floating-button";
import { DynamicBreadcrumb } from "~/components/dynamic-breadcrumb/DynamicBreadcrumb";
import {
  generateEducationalOrgSchema,
  generateLocalBusinessSchema,
  generateWebsiteSchema,
} from "~/lib/structured-data";

export const metadata: Metadata = {
  title: {
    template: "%s | Aims Academy Bangalore",
    default:
      "Aims Academy | Top NEET Long Term Coaching & PU Integrated Coaching Center in Bangalore",
  },
  description:
    "Aims Academy –  Top NEET Long Term Coaching & PU Integrated Coaching Center in Bangalore. Expert faculty, personalized mentoring & proven strategies for NEET, JEE & KCET. Enroll now!",
  keywords: [
    "BEST NEET Coaching Bangalore",
    "PUC Coaching Bangalore",
    "PCMB Courses Yelahanka",
    "Best IIT & JEE Coaching in Academy North Bangalore",
    "PU College Near RTO Office",
    "Karnataka PUC Top Rankers",
  ],
  metadataBase: new URL(SITE_DOMAIN),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    title: "Aims Academy | Top NEET & PU College in Bangalore",
    description:
      "Aims Academy –  Top NEET Long Term Coaching & PU Integrated Coaching Center in Bangalore",
    url: SITE_DOMAIN,
    siteName: "Aims Academy",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Aims Academy Campus in Yelahanka",
      },
    ],
    locale: "en_IN",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Aims Academy | Bangalore's Best PUC Coaching",
    description:
      "Ranked #1 NEET & PU College in Yelahanka. 9008466404 | RTO Office Road",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  viewportFit: "cover",
};

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html
      lang="en"
      className={`${GeistSans.variable}`}
      suppressHydrationWarning
    >
      <head>
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        {/* Critical CSS - inline the most important styles */}
        <style
          dangerouslySetInnerHTML={{
            __html: `
              /* Critical base styles */
              *,::before,::after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}
              ::before,::after{--tw-content:''}
              html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal}
              body{margin:0;line-height:inherit}

              /* Critical color variables */
              :root{
                --background:32 0% 100%;
                --foreground:32 0% 10%;
                --primary:32 94% 54.1%;
                --primary-foreground:0 0% 100%;
                --border:32 20% 82%;
                --ring:32 94% 54.1%;
              }
              .dark{
                --background:32 10% 10%;
                --foreground:32 0% 100%;
                --primary:32 94% 54.1%;
                --primary-foreground:0 0% 100%;
                --border:32 20% 50%;
                --ring:32 94% 54.1%;
              }

              /* Critical layout styles */
              body{background-color:hsl(var(--background));color:hsl(var(--foreground))}
              .p-4{padding:1rem}

              /* Hide content until styles load to prevent FOUC */
              .loading-placeholder{opacity:0;transition:opacity 0.1s ease-in}
              .styles-loaded .loading-placeholder{opacity:1}
            `,
          }}
        />
      </head>
      <body className="loading-placeholder">
        <NextTopLoader showSpinner={false} color="#023CB6" />
        <ThemeProvider attribute="class" enableSystem>
          <NavMenuWrapper />
          <main className="p-4">
            <DynamicBreadcrumb />
            {children}
          </main>
          <Footer />
          <Toaster />
          <WhatsAppButton />
        </ThemeProvider>
        <GoogleAnalytics gaId="G-SS85E0SRMD" />
        <GoogleTagManager gtmId="GTM-WTGKZGGT" />

        {/* Structured data for SEO - loaded after page is interactive */}
        <Script
          id="organization-schema"
          type="application/ld+json"
          strategy="afterInteractive"
        >
          {JSON.stringify(generateEducationalOrgSchema())}
        </Script>

        {/* LocalBusiness schema for SEO */}
        <Script
          id="local-business-schema"
          type="application/ld+json"
          strategy="afterInteractive"
        >
          {JSON.stringify(generateLocalBusinessSchema())}
        </Script>

        {/* Website schema for SEO */}
        <Script
          id="website-schema"
          type="application/ld+json"
          strategy="afterInteractive"
        >
          {JSON.stringify(generateWebsiteSchema())}
        </Script>

        {/* Preload and async load main CSS to prevent render blocking */}
        <Script
          id="load-main-css"
          strategy="beforeInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                // Preload CSS for faster loading
                var preload = document.createElement('link');
                preload.rel = 'preload';
                preload.as = 'style';
                preload.href = '/styles/globals.css';
                document.head.appendChild(preload);

                // Load CSS asynchronously
                var link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = '/styles/globals.css';
                link.media = 'print';
                link.onload = function() {
                  this.media = 'all';
                  document.documentElement.classList.add('styles-loaded');
                };
                document.head.appendChild(link);

                // Fallback for browsers that don't support onload
                setTimeout(function() {
                  link.media = 'all';
                  document.documentElement.classList.add('styles-loaded');
                }, 50);
              })();
            `,
          }}
        />
      </body>
    </html>
  );
}
