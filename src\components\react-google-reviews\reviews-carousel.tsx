"use client";

import { type ReviewCardProps, ReviewCard } from "./review-card";
import {
  Carousel,
  CarouselContent,
  CarouselIndicator,
  CarouselItem,
} from "~/components/motion-primitives/carousel";
import { isMobile, isTablet } from "react-device-detect";
import ScrollingTestimonials from "../animata/container/scrolling-testimonials";
import { useEffect, useState } from "react";

interface ReviewsCarouselProps {
  reviews: ReviewCardProps[];
}

export function ReviewsCarouselClient({ reviews }: ReviewsCarouselProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!reviews?.length) {
    return (
      <p className="text-center text-muted-foreground">No reviews available.</p>
    );
  }

  if (!isClient) {
    return <div className="my-12 h-[300px] w-full" />; // Loading placeholder
  }

  // Limit reviews based on device type
  const slideCount = isMobile ? 5 : isTablet ? 7 : 13;
  const mobileReviews = reviews.slice(0, slideCount);

  return (
    <div className="my-12 flex items-center justify-center">
      {isMobile || isTablet ? (
        <div className="max-w-xs">
          <Carousel>
            <CarouselContent className="mb-6">
              {mobileReviews.map((review, index) => (
                <CarouselItem key={index}>
                  <ReviewCard {...review} />
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselIndicator />
          </Carousel>
        </div>
      ) : (
        <div className="w-full">
          <ScrollingTestimonials data={reviews} />
        </div>
      )}
    </div>
  );
}
