import type { Metadata } from "next";
import { CoursesPageHero } from "~/components/courses-components/courses-page-hero/courses-page-hero";
import { ProgramFeatureSection } from "~/components/courses-components/feature-cards-section";
import { TrainingProcessSection } from "~/components/courses-components/training-process-section";
import { WhyChooseUs } from "~/components/courses-components/why-choose-us";
import { puCourseProgramFeatureList } from "~/constants/courses";

export const metadata: Metadata = {
  title: "PU Courses in Bangalore | PCMB & PCMC Science Streams",
  description:
    "Enroll in Aims Academy's PU courses in Bangalore. Offering PCMB and PCMC science streams with expert faculty, competitive exam preparation, and personalized mentoring. Build a strong academic foundation for NEET, JEE, KCET, and more.",
  keywords: [
    "PU Courses Bangalore",
    "PCMB Courses Bangalore",
    "PCMC Coaching Bangalore",
    "Science PU Classes Bangalore",
    "Best PU College Bangalore",
    "PU Integrated Coaching",
  ],
  openGraph: {
    title: "PU Courses in Bangalore | PCMB & PCMC Science Streams",
    description:
      "Join Aims Academy for top PU courses in Bangalore. Offering PCMB and PCMC streams with expert guidance, competitive exam preparation, and proven results.",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Aims Academy PU Courses",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "PU Courses in Bangalore | PCMB & PCMC Science Streams",
    description:
      "Achieve academic excellence with Aims Academy's PU courses in Bangalore. Expert coaching for PCMB and PCMC streams. Enroll now!",
  },
};

const PuCourse = () => {
  const heroProps = {
    heading: "PU Courses",
    subheading: "Science Streams - PCMB, PCMC",
    description:
      "Our PU Courses provide a strong foundation in Science streams such as PCMB (Physics, Chemistry, Mathematics, Biology) and PCMC (Physics, Chemistry, Mathematics, Computer Science). Designed to prepare students for competitive exams like NEET, JEE, and KCET while ensuring academic excellence. Join Aims Academy to excel in your studies and build a successful future.",
    buttons: {
      primary: {
        text: "Enroll Now",
        url: "#contact-form",
      },
      secondary: {
        text: "Learn More",
        url: "#contact-form",
      },
    },
    image: {
      src: "/graduation_courses.svg",
      alt: "PU Course",
    },
  };

  return (
    <article>
      <CoursesPageHero {...heroProps} />
      <ProgramFeatureSection
        features={puCourseProgramFeatureList}
        imageSrc="/program-feature-img.png"
      />
      <WhyChooseUs />
      <TrainingProcessSection courseType="PU_COURSE" />
    </article>
  );
};

export default PuCourse;
