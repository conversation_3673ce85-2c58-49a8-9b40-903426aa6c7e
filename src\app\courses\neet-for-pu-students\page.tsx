import type { Metadata } from "next";
import { CoursesPageHero } from "~/components/courses-components/courses-page-hero/courses-page-hero";
import { ProgramFeatureSection } from "~/components/courses-components/feature-cards-section";
import { TrainingProcessSection } from "~/components/courses-components/training-process-section";
import { WhyChooseUs } from "~/components/courses-components/why-choose-us";
import { neetProgramFeatureList } from "~/constants/courses";

export const metadata: Metadata = {
  title: "NEET Coaching for PU Students | Aims Academy",
  description:
    "Prepare for NEET with Aims Academy's expert coaching for PU students. Comprehensive syllabus coverage, personalized mentoring, and regular mock tests to ensure success.",
  keywords: [
    "NEET Coaching for PU Students",
    "NEET Preparation Bangalore",
    "PU NEET Integrated Program",
    "Best NEET Coaching for PUC Students",
    "NEET Classes for PU Students",
  ],
  openGraph: {
    title: "NEET Coaching for PU Students | Aims Academy",
    description:
      "Join Aims Academy for NEET coaching tailored for PU students. Expert guidance, comprehensive syllabus coverage, and proven results. Enroll now!",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Aims Academy NEET Coaching for PU Students",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "NEET Coaching for PU Students | Aims Academy",
    description:
      "Excel in NEET with Aims Academy's expert coaching for PU students. Comprehensive preparation and personalized mentoring for success!",
  },
};

const heroProps = {
  heading: "NEET Preparation for PU Students",
  subheading: "Comprehensive Coaching for Medical Aspirants",
  description:
    "Unlock your medical career potential with Aims Academy's NEET coaching program tailored for PU students. Our comprehensive syllabus coverage, expert faculty, and personalized mentoring ensure you excel in NEET and secure admission to top medical colleges.",
  buttons: {
    primary: {
      text: "Enroll Now",
      url: "#contact-form",
    },
    secondary: {
      text: "Learn More",
      url: "#contact-form",
    },
  },
  image: {
    src: "/doctor_courses.svg",
    alt: "NEET for PU Students",
  },
};

const NeetForPuStudents = () => {
  return (
    <article>
      <CoursesPageHero {...heroProps} />
      <ProgramFeatureSection
        features={neetProgramFeatureList}
        imageSrc="/program-feature-img.png"
      />
      <WhyChooseUs />
      <TrainingProcessSection courseType="NEET_FOR_PU_STUDENTS" />
    </article>
  );
};

export default NeetForPuStudents;
