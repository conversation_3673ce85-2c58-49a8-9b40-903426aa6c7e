import Image from "next/image";

export interface ProgramFeatureItem {
  title: string;
  details: string[];
}

export interface ProgramFeatureCardsSectionProps {
  title?: string;
  features: ProgramFeatureItem[];
  imageSrc?: string;
}

export function ProgramFeatureSection({
  title = "Program Features",
  features = [
    {
      title: "Dedicated Guidance",
      details: [
        "Personal Mentors: Each student is assigned a personal mentor",
        "Subject Experts: Experienced faculty in all subjects",
        "Regular Check-ins: Meetings to track progress",
      ],
    },
    {
      title: "Study Materials",
      details: [
        "Comprehensive Resources: Textbooks, worksheets and digital resources",
        "Updated Syllabus Coverage: Always current with latest syllabus",
        "Access to Online Library: Video lectures and interactive quizzes",
      ],
    },
    {
      title: "Regular Assessments",
      details: [
        "Weekly Tests: Reinforce key concepts",
        "Full-Length Mock Exams: Simulate actual test experience",
        "Detailed Analysis: Performance insights",
      ],
    },
  ],
  imageSrc,
}: ProgramFeatureCardsSectionProps) {
  return (
    <section className="mx-auto px-4 py-12">
      <h2 className="mb-12 text-center text-3xl font-bold">{title}</h2>

      <div className="flex flex-col items-center gap-12 lg:flex-row">
        {imageSrc && (
          <div className="flex-shrink-0">
            <Image
              src={imageSrc}
              alt="Program Features"
              width={400}
              height={400}
              className="rounded-lg object-cover"
            />
          </div>
        )}

        <div className="flex-1">
          {features.map((feature, index) => (
            <div key={index} className="mb-8">
              <h3 className="mb-4 text-2xl font-semibold text-primary">
                {feature.title}
              </h3>
              <ul className="space-y-2">
                {feature.details.map((detail, i) => (
                  <li key={i} className="flex items-start">
                    <span className="mr-2">•</span>
                    <span>{detail}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
