import Script from "next/script";
import { cn } from "~/lib/utils";
import { generatePlaceSchema } from "~/lib/structured-data";

const GoogleMap = ({ className }: { className?: string }) => {
  return (
    <div
      className={cn(
        "relative mx-auto mt-2 w-full overflow-hidden rounded-lg shadow-md",
        className,
      )}
    >
      <iframe
        title="aims academy google map"
        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3885.2890150846333!2d77.5608711!3d13.144160800000002!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3bae23eb0b278729%3A0xf600c4d8f6399e0c!2sAIMS%20ACADEMY%20-%20PU%20%7C%7C%20NEET%20%7C%7C%20JEE%20%7C%7C%20KCET%20%7C%7C%20Bangalore!5e0!3m2!1sen!2sin!4v1741075763984!5m2!1sen!2sin"
        className="h-[300px] w-full sm:h-[450px]" // Responsive height
        style={{ border: 0 }}
        allowFullScreen
        loading="lazy"
        referrerPolicy="no-referrer-when-downgrade"
      />
      <Script
        id="google-map-place-schema"
        type="application/ld+json"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(generatePlaceSchema()),
        }}
      />
    </div>
  );
};

export default GoogleMap;
