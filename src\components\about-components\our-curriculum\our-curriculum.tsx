"use client"; // Required for Framer Motion

import { motion } from "framer-motion";
import { PersonStanding, Timer, Zap, ZoomIn } from "lucide-react";

interface Feature {
  title: string;
  description: string;
  icon: JSX.Element;
}

interface Feature17Props {
  heading?: string;
  features?: Feature[];
}

// Animation variants for Framer Motion
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

const OurCurriculum = ({
  heading = "Pillars of our curriculum",
  features = [
    {
      title: "Teach",
      description:
        "Using amicable and appropriate teaching methods to make arduous concepts easier",
      icon: <Timer className="size-4 md:size-6" />,
    },
    {
      title: "Test",
      description:
        "Standardized test pattern & frequency to assess the cognitive abilities of the students",
      icon: <Zap className="size-4 md:size-6" />,
    },
    {
      title: "Transvaluate",
      description:
        "To understand the status of student’s performance, strength and weakness using certain parameters unlike marks",
      icon: <ZoomIn className="size-4 md:size-6" />,
    },
    {
      title: "Troubleshoot",
      description:
        "Recognizing the difficulties in the learning process and suggesting the fit as a fiddle method to cope up",
      icon: <PersonStanding className="size-4 md:size-6" />,
    },
  ],
}: Feature17Props) => {
  return (
    <section className="py-16">
      <div className="mx-auto max-w-screen-xl">
        {/* Heading with animation */}
        <motion.h2
          initial="hidden"
          whileInView="visible"
          variants={fadeInUp}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mb-2 text-center text-3xl font-semibold lg:text-5xl"
        >
          {heading}
        </motion.h2>

        {/* Features grid with animations */}
        <div className="mx-auto mt-14 grid gap-x-20 gap-y-8 md:grid-cols-2 md:gap-y-6 lg:mt-20">
          {features.map((feature, idx) => (
            <motion.div
              key={idx}
              initial="hidden"
              whileInView="visible"
              variants={fadeInUp}
              transition={{ duration: 0.6, delay: idx * 0.2 }}
              viewport={{ once: true }}
              className="flex gap-6 rounded-lg md:block md:p-5"
            >
              {/* Icon */}
              <span className="mb-8 flex size-10 shrink-0 items-center justify-center rounded-full bg-accent md:size-12">
                {feature.icon}
              </span>

              {/* Content */}
              <div>
                <h3 className="mb-2 text-xl font-semibold">{feature.title}</h3>
                <p className="text-muted-foreground">{feature.description}</p>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export { OurCurriculum };
