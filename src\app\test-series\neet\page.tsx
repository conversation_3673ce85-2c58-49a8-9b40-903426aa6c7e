import { type <PERSON>ada<PERSON> } from "next";
import { Card, CardContent } from "~/components/ui/card";
import Faq02 from "~/components/kokonutui/faq-02";
import Script from "next/script";
import { SITE_DOMAIN } from "~/constants/siteConfig";
import { generateCourseSchema, generateFaqSchema } from "~/lib/structured-data";
import Link from "next/link";
import { ArrowRight, Calendar } from "lucide-react";
import { cn } from "~/lib/utils";
import { buttonVariants } from "~/components/ui/button";

// Define NEET test series FAQs
const NEET_TEST_SERIES_FAQS = [
  {
    question: "What is included in the NEET Test Series?",
    answer:
      "Our NEET Test Series includes 3 full-length mock tests with 180 questions each, subject-wise tests for Physics, Chemistry, and Biology, chapter-wise practice questions, previous years' papers with solutions, and performance analysis tools.",
  },
  {
    question: "How will the Test Series help in my NEET preparation?",
    answer:
      "The Test Series provides exam-like practice, helps identify weak areas, improves time management, builds confidence, and familiarizes you with the actual exam pattern and difficulty level.",
  },
  {
    question:
      "Is the Test Series updated according to the latest NEET pattern?",
    answer:
      "Yes, our Test Series is regularly updated to reflect the latest NEET exam pattern, syllabus changes, and question trends to ensure you're practicing with the most relevant material.",
  },
  {
    question: "How do I access the Test Series after submitting the form?",
    answer:
      "After submitting the form, you'll receive the test series materials via email with instructions on how to access the online test platform or use the offline materials.",
  },
  {
    question: "Are detailed solutions provided for all questions?",
    answer:
      "Yes, comprehensive solutions with step-by-step explanations are provided for all questions to help you understand concepts better and learn from your mistakes.",
  },
  {
    question: "Can I track my performance across multiple tests?",
    answer:
      "Yes, our Test Series includes performance tracking tools that allow you to monitor your progress, identify improvement areas, and compare your performance with previous attempts.",
  },
  {
    question: "Is the Test Series suitable for first-time NEET aspirants?",
    answer:
      "Absolutely! The Test Series is designed for both first-time aspirants and repeaters. It starts with fundamental concepts and gradually increases in difficulty to match the NEET exam level.",
  },
  {
    question: "How is this Test Series different from others available?",
    answer:
      "Our Test Series is created by experienced faculty with deep understanding of NEET patterns, offers more comprehensive coverage, provides detailed analytics, and is regularly updated based on the latest exam trends.",
  },
];

// Define metadata for SEO
export const metadata: Metadata = {
  title: "NEET Test Series | Aims Academy",
  description:
    "Prepare effectively for NEET with Aims Academy's comprehensive test series. Practice with exam-like questions, get detailed solutions, and improve your score.",
  keywords: [
    "NEET Test Series",
    "NEET Preparation",
    "NEET Mock Tests",
    "NEET Practice Papers",
    "NEET Exam Preparation",
    "Medical Entrance Test Series",
    "NEET Question Bank",
    "Aims Academy Test Series",
  ],
  alternates: {
    canonical: `${SITE_DOMAIN}/test-series/neet`,
  },
  openGraph: {
    title: "NEET Test Series | Aims Academy",
    description:
      "Comprehensive NEET test series with exam-like questions, detailed solutions, and performance analysis. Prepare effectively for NEET.",
    url: `${SITE_DOMAIN}/test-series/neet`,
    siteName: "Aims Academy",
    images: [
      {
        url: `${SITE_DOMAIN}/og-image.jpg`,
        width: 1200,
        height: 630,
        alt: "Aims Academy NEET Test Series",
      },
    ],
    locale: "en_IN",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "NEET Test Series | Aims Academy",
    description:
      "Comprehensive NEET test series with exam-like questions, detailed solutions, and performance analysis. Prepare effectively for NEET.",
    images: [`${SITE_DOMAIN}/og-image.jpg`],
  },
};

// Define available NEET test series years
const availableYears = [
  {
    year: "2025",
    title: "NEET 2025 Test Series",
    description:
      "Comprehensive test series for NEET 2025 with mock tests, subject-wise tests, and detailed solutions.",
    href: "/test-series/neet/2025",
    featured: true,
    status: "Available Now",
  },
  {
    year: "2026",
    title: "NEET 2026 Test Series",
    description:
      "Early preparation test series for NEET 2026 aspirants with foundation-level questions and concept-building tests.",
    href: "#",
    status: "Coming Soon",
    disabled: true,
  },
];

export default function NEETTestSeriesPage() {
  return (
    <article className="mx-auto max-w-6xl">
      {/* Hero Section */}
      <section className="mb-12 text-center">
        <h1 className="mb-4 text-4xl font-bold">NEET Test Series</h1>
        <p className="mx-auto max-w-3xl text-lg text-muted-foreground">
          Prepare effectively for the National Eligibility cum Entrance Test
          (NEET) with our comprehensive test series designed by expert faculty.
          Practice with exam-like questions and improve your score.
        </p>
      </section>

      {/* Introduction Section */}
      <section className="mb-16">
        <div className="grid gap-8 md:grid-cols-2">
          <div>
            <h2 className="mb-4 text-2xl font-semibold">
              Why NEET Mock Tests Matter
            </h2>
            <p className="mb-4 text-muted-foreground">
              NEET is among the toughest entrance exams in India, with millions
              of aspirants competing for limited medical and dental seats. The
              exam evaluates students&apos; understanding of Physics, Chemistry,
              and Biology.
            </p>
            <p className="mb-4 text-muted-foreground">
              To achieve success in NEET, practicing consistently with mock
              tests is critical. These tests mimic the real exam&apos;s
              structure, providing a firsthand experience of the pressure and
              time constraints candidates will face.
            </p>
            <p className="text-muted-foreground">
              Our NEET Test Series serves as a valuable tool for aspirants to
              grasp the exam pattern and key topics from the perspective of the
              actual exam. It aids in assessing one&apos;s preparation and
              refining time management skills specific to the NEET exam.
            </p>
          </div>
          <div className="flex items-center justify-center">
            <div className="rounded-lg bg-muted p-6">
              <h3 className="mb-4 text-xl font-medium">NEET Exam Pattern</h3>
              <table className="w-full border-collapse">
                <tbody>
                  <tr className="border-b">
                    <td className="py-2 font-medium">Total Questions</td>
                    <td className="py-2">180 Questions</td>
                  </tr>
                  <tr className="border-b">
                    <td className="py-2 font-medium">Total Marks</td>
                    <td className="py-2">720 Marks</td>
                  </tr>
                  <tr className="border-b">
                    <td className="py-2 font-medium">Subjects</td>
                    <td className="py-2">
                      Physics, Chemistry, Botany, Zoology
                    </td>
                  </tr>
                  <tr className="border-b">
                    <td className="py-2 font-medium">Questions per Subject</td>
                    <td className="py-2">45</td>
                  </tr>
                  <tr className="border-b">
                    <td className="py-2 font-medium">Marking Scheme</td>
                    <td className="py-2">+4 for correct, -1 for incorrect</td>
                  </tr>
                  <tr>
                    <td className="py-2 font-medium">Duration</td>
                    <td className="py-2">3 hours</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      {/* Available Years Section */}
      <section className="mb-16">
        <h2 className="mb-8 text-center text-2xl font-semibold">
          Available NEET Test Series
        </h2>
        <div className="grid gap-8 md:grid-cols-2">
          {availableYears.map((item, index) => (
            <Card
              key={index}
              className={cn(
                "border-primary/20 transition-all duration-300 hover:shadow-lg",
                item.featured && "border-primary/50 ring-1 ring-primary/20",
              )}
            >
              <CardContent className="p-6">
                <div className="mb-4 flex items-center gap-3">
                  <Calendar className="h-8 w-8 text-primary" />
                  <h3 className="text-xl font-medium">{item.title}</h3>
                  {item.featured && (
                    <span className="ml-auto rounded-full bg-primary/10 px-2 py-1 text-xs font-medium text-primary">
                      Featured
                    </span>
                  )}
                </div>
                <p className="mb-4 text-muted-foreground">{item.description}</p>
                <div className="mb-4 flex items-center">
                  <span className="rounded-full bg-green-100 px-3 py-1 text-sm font-medium text-green-800 dark:bg-green-900/30 dark:text-green-400">
                    {item.status}
                  </span>
                </div>
                {item.disabled ? (
                  <div
                    className={cn(
                      buttonVariants({ variant: "outline" }),
                      "w-full cursor-not-allowed justify-between opacity-60",
                    )}
                  >
                    View {item.year} Test Series
                    <ArrowRight className="h-4 w-4" />
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    className={cn(
                      buttonVariants({ variant: "outline" }),
                      "w-full justify-between",
                    )}
                  >
                    View {item.year} Test Series
                    <ArrowRight className="h-4 w-4" />
                  </Link>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* Features Section */}
      <section className="mb-16">
        <h2 className="mb-8 text-center text-2xl font-semibold">
          Key Features of Our NEET Test Series
        </h2>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card className="border-primary/20">
            <CardContent className="p-6">
              <h3 className="mb-2 text-xl font-medium">Exam-Like Experience</h3>
              <p>
                Questions designed to match the actual NEET exam pattern and
                difficulty level.
              </p>
            </CardContent>
          </Card>
          <Card className="border-primary/20">
            <CardContent className="p-6">
              <h3 className="mb-2 text-xl font-medium">
                Comprehensive Coverage
              </h3>
              <p>
                Covers all important topics and concepts from Physics,
                Chemistry, and Biology.
              </p>
            </CardContent>
          </Card>
          <Card className="border-primary/20">
            <CardContent className="p-6">
              <h3 className="mb-2 text-xl font-medium">Detailed Solutions</h3>
              <p>
                Each question comes with detailed explanations to help you
                understand concepts better.
              </p>
            </CardContent>
          </Card>
          <Card className="border-primary/20">
            <CardContent className="p-6">
              <h3 className="mb-2 text-xl font-medium">Performance Analysis</h3>
              <p>
                Track your progress and identify weak areas to focus your
                preparation.
              </p>
            </CardContent>
          </Card>
          <Card className="border-primary/20">
            <CardContent className="p-6">
              <h3 className="mb-2 text-xl font-medium">Expert-Designed</h3>
              <p>
                Created by experienced faculty with deep understanding of NEET
                examination trends.
              </p>
            </CardContent>
          </Card>
          <Card className="border-primary/20">
            <CardContent className="p-6">
              <h3 className="mb-2 text-xl font-medium">Regular Updates</h3>
              <p>
                Test series is regularly updated to reflect the latest exam
                patterns and trends.
              </p>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* What's Included Section */}
      <section className="mb-16">
        <h2 className="mb-8 text-center text-2xl font-semibold">
          What&apos;s Included in Our NEET Test Series
        </h2>
        <div className="rounded-lg bg-muted p-6">
          <ul className="ml-6 list-disc space-y-2">
            <li>3 Full-length NEET mock tests with 180 questions each</li>
            <li>Subject-wise tests for Physics, Chemistry, and Biology</li>
            <li>Chapter-wise practice questions with detailed solutions</li>
            <li>Previous years&apos; question papers with solutions</li>
            <li>Performance analysis and progress tracking</li>
            <li>Tips and strategies for effective NEET preparation</li>
          </ul>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="mb-16">
        <h2 className="mb-8 text-center text-2xl font-semibold">
          Benefits of Solving NEET Mock Tests
        </h2>
        <div className="rounded-lg bg-muted p-6">
          <ul className="ml-6 list-disc space-y-3">
            <li>
              <span className="font-medium">Exam Familiarity:</span> By solving
              NEET mock tests, aspirants can gather a clear picture of the exam
              pattern and get familiar with the types of questions asked.
            </li>
            <li>
              <span className="font-medium">Speed and Time Management:</span>{" "}
              Attempting more mock tests will help increase your speed and time
              management skills for the NEET exam.
            </li>
            <li>
              <span className="font-medium">Subject-wise Preparation:</span> You
              can attempt subject-wise mock tests to prepare well for all
              subjects individually.
            </li>
            <li>
              <span className="font-medium">Identify Weaknesses:</span> Through
              the exam summary and results, you&apos;ll be able to identify your
              weak and strong areas.
            </li>
            <li>
              <span className="font-medium">Improvement Opportunities:</span>{" "}
              After identifying mistakes while solving the mock tests, you can
              work on those areas to improve your performance.
            </li>
            <li>
              <span className="font-medium">Confidence Booster:</span> Regular
              practice with mock tests works as a confidence booster for
              aspirants.
            </li>
          </ul>
        </div>
      </section>

      {/* Call to Action */}
      <section className="mb-16">
        <div className="rounded-lg bg-primary/10 p-8 text-center">
          <h2 className="mb-4 text-2xl font-bold">
            Ready to Start Your NEET Preparation?
          </h2>
          <p className="mb-6 text-lg">
            Choose a test series year to begin your journey towards NEET
            success.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            {availableYears.map((item, index) =>
              item.disabled ? (
                <div
                  key={index}
                  className={cn(
                    buttonVariants({ size: "lg" }),
                    "cursor-not-allowed opacity-60",
                  )}
                >
                  {item.year} Test Series
                </div>
              ) : (
                <Link
                  key={index}
                  href={item.href}
                  className={buttonVariants({ size: "lg" })}
                >
                  {item.year} Test Series
                </Link>
              ),
            )}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <Faq02 items={NEET_TEST_SERIES_FAQS} />

      {/* Structured Data */}
      <Script
        id="test-series-schema"
        type="application/ld+json"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(
            generateCourseSchema({
              name: "NEET Test Series",
              description:
                "Comprehensive test series for NEET preparation with mock tests, subject-wise tests, and detailed solutions.",
              courseCode: "NEET-TS",
            }),
          ),
        }}
      />

      <Script
        id="faq-schema"
        type="application/ld+json"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(generateFaqSchema(NEET_TEST_SERIES_FAQS)),
        }}
      />
    </article>
  );
}
