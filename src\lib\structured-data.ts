import { SITE_CONFIG, SITE_DOMAIN } from "~/constants/siteConfig";

// ============================================================================
// Schema.org Type Definitions
// ============================================================================

/**
 * Interface for educational service
 */
interface EducationalService {
  name: string;
  url: string;
  description: string;
}

/**
 * Interface for schema.org Service type
 */
interface ServiceSchema {
  "@type": "Service";
  name: string;
  url: string;
  description: string;
  provider: {
    "@type": "EducationalOrganization";
    name: string;
    sameAs: string;
  };
  areaServed: {
    "@type": string;
    name: string;
  };
}

/**
 * Interface for Course schema parameters
 */
interface CourseSchemaParams {
  name: string;
  description: string;
  courseCode?: string;
  provider?: string | Record<string, unknown>;
}

/**
 * Interface for Product Review schema parameters
 */
interface ProductReviewSchemaParams {
  ratingValue?: string;
  ratingCount?: string;
  description?: string;
  image?: string;
}

// ============================================================================
// Schema Generator Functions
// ============================================================================

/**
 * Generates organization schema for the website
 */
export function generateOrganizationSchema(
  logoTitle: string,
  logoSrc: string,
  tagline: string,
) {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: logoTitle,
    url: SITE_DOMAIN,
    logo: `${SITE_DOMAIN}${logoSrc}`,
    description: tagline,
    contactPoint: {
      "@type": "ContactPoint",
      telephone: "+919008266404",
      contactType: "customer service",
      areaServed: "IN",
      availableLanguage: "en",
    },
    sameAs: [SITE_CONFIG.social.instagram, SITE_CONFIG.social.facebook],
  };
}

/**
 * Generates LocalBusiness schema for the website
 */
export function generateLocalBusinessSchema() {
  // Define service areas in Bangalore
  const serviceAreas = [
    "Yelahanka",
    "North Bangalore",
    "Hebbal",
    "Sahakarnagar",
    "RT Nagar",
    "Jakkur",
    "Kodigehalli",
    "Vidyaranyapura",
    "Devanahalli",
    "Bagalur",
  ];

  return {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    name: SITE_CONFIG.name,
    image: `${SITE_DOMAIN}/logo.png`,
    "@id": SITE_DOMAIN,
    url: SITE_DOMAIN,
    telephone: "+91 90082 66404",
    address: {
      "@type": "PostalAddress",
      streetAddress:
        "Survey No: 92/5, RTO Office Road, Yelahanka Hobli, Singanayakanahalli,",
      addressLocality: "Bengaluru",
      postalCode: "560064",
      addressCountry: "IN",
    },
    areaServed: [
      {
        "@type": "City",
        name: "Bangalore",
        containsPlace: serviceAreas.map((area) => ({
          "@type": "Place",
          name: `${area}, Bangalore`,
        })),
      },
    ],
    description:
      "Top NEET Long Term Coaching & PU Integrated Coaching Center in Bangalore. Expert faculty, personalized mentoring & proven strategies for NEET, JEE & KCET.",
    keywords:
      "NEET Coaching in Bangalore, NEET Long Term Coaching, PU College, JEE Coaching, KCET Preparation",
    sameAs: [SITE_CONFIG.social.instagram, SITE_CONFIG.social.facebook],
  };
}

/**
 * Generates WebSite schema for the website
 */
export function generateWebsiteSchema() {
  return {
    "@context": "https://schema.org/",
    "@type": "WebSite",
    name: SITE_CONFIG.name,
    url: SITE_DOMAIN,
    potentialAction: {
      "@type": "SearchAction",
      target: `${SITE_DOMAIN}/search?q={search_term_string}`,
      "query-input": "required name=search_term_string",
    },
  };
}

/**
 * Generates educational organization schema for the website
 */
export function generateEducationalOrgSchema() {
  return {
    "@context": "https://schema.org",
    "@type": "EducationalOrganization",
    name: SITE_CONFIG.name,
    alternateName: "Aims Academy Bangalore",
    image: "/logo.jpg",
    "@id": SITE_DOMAIN,
    url: SITE_DOMAIN,
    telephone: "+919008266404",
    address: {
      "@type": "PostalAddress",
      streetAddress: "Survey No: 92/5, RTO Office Road",
      addressLocality: "Singanayakanahalli",
      addressRegion: "Karnataka",
      postalCode: "560064",
      addressCountry: "IN",
    },
    geo: {
      "@type": "GeoCoordinates",
      latitude: 13.144161,
      longitude: 77.560871,
    },
    description:
      "Top NEET Long Term Coaching & PU Integrated Coaching Center in Bangalore. Expert faculty for NEET, JEE & KCET preparation.",
    areaServed: {
      "@type": "City",
      name: "Bangalore",
    },
    hasOfferCatalog: {
      "@type": "OfferCatalog",
      name: "Educational Programs",
      itemListElement: [
        {
          "@type": "Offer",
          itemOffered: generateCourseSchema({
            name: "NEET Long Term Coaching in Bangalore",
            description:
              "Comprehensive NEET preparation program with expert faculty",
            courseCode: "NEET-LT-BLR",
            provider: SITE_CONFIG.name,
          }),
        },
        {
          "@type": "Offer",
          itemOffered: generateCourseSchema({
            name: "PU Integrated NEET Coaching",
            description: "Integrated PU and NEET preparation program",
            courseCode: "PU-NEET-INTEGRATED",
            provider: SITE_CONFIG.name,
          }),
        },
        {
          "@type": "Offer",
          itemOffered: generateCourseSchema({
            name: "JEE Coaching in Bangalore",
            description: "Expert coaching for JEE preparation",
            courseCode: "JEE-BLR",
            provider: SITE_CONFIG.name,
          }),
        },
      ],
    },
    sameAs: [
      SITE_CONFIG.social.facebook,
      SITE_CONFIG.maps.url,
      SITE_CONFIG.social.instagram,
    ],
  };
}

/**
 * Generates review schema for the website
 */
export function generateReviewSchema(
  name = "Google Review User",
  rating = "5",
  date: string = new Date().toISOString(),
  comment = "Excellent educational institution",
) {
  return {
    "@context": "https://schema.org",
    "@type": "Review",
    itemReviewed: {
      "@type": "Organization",
      name: SITE_CONFIG.name,
      url: "https://aimsacademy.in",
    },
    reviewRating: {
      "@type": "Rating",
      ratingValue: rating,
      bestRating: "5",
    },
    author: {
      "@type": "Person",
      name: name,
    },
    datePublished: date,
    reviewBody: comment,
  };
}

/**
 * Generates place schema for Google Maps
 */
export function generatePlaceSchema() {
  return {
    "@context": "https://schema.org",
    "@type": "Place",
    name: "AIMS ACADEMY - PU || NEET || JEE || KCET || Bangalore",
    image: [`${SITE_DOMAIN}/nextImageExportOptimizer/logo-opt-256.WEBP`],
    address: {
      "@type": "PostalAddress",
      streetAddress: "Survey No: 92/5, RTO Office Road",
      addressLocality: "Singanayakanahalli",
      addressRegion: "Karnataka",
      postalCode: "560064",
      addressCountry: "IN",
    },
    geo: {
      "@type": "GeoCoordinates",
      latitude: 13.144161,
      longitude: 77.560871,
    },
    url: SITE_CONFIG.maps.url,
    telephone: "+919008266404",
  };
}

/**
 * Generates product review schema for the website with aggregate ratings
 * @param ratingValue - The average rating value (e.g., "4.8")
 * @param ratingCount - The total number of ratings/reviews (e.g., "500")
 * @param description - Optional custom description
 * @param image - Optional custom image URL (relative to SITE_DOMAIN)
 */

/**
 * Generates Course review schema for the website with aggregate ratings and sample reviews
 * @param ratingValue - The average rating value (e.g., "4.8")
 * @param ratingCount - The total number of ratings/reviews (e.g., "500")
 * @param description - Optional custom description
 * @param image - Optional custom image URL (relative to SITE_DOMAIN)
 * @param reviews - Optional array of review objects
 */
export function generateProductReviewSchema({
  ratingValue = "4.8",
  ratingCount = "500",
  description = "Top NEET & PU College in Bangalore offering top-quality coaching for NEET, JEE, and PU courses in Bangalore.",
  image = "/logo.png",
  reviews = [
    {
      author: "Google User",
      datePublished: "2024-07-01",
      reviewBody: "Excellent coaching and faculty! Highly recommend.",
      ratingValue: "5",
    },
    {
      author: "Student A",
      datePublished: "2024-06-15",
      reviewBody: "Great results and personal attention.",
      ratingValue: "5",
    },
  ],
}: ProductReviewSchemaParams & {
  reviews?: Array<{
    author: string;
    datePublished: string;
    reviewBody: string;
    ratingValue: string;
  }>;
} = {}) {
  return {
    "@context": "https://schema.org",
    "@type": "Course",
    name: SITE_CONFIG.name,
    image: `${SITE_DOMAIN}${image}`,
    description: description,
    provider: {
      "@type": "EducationalOrganization",
      name: SITE_CONFIG.name,
      sameAs: SITE_DOMAIN,
    },
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: ratingValue,
      bestRating: "5",
      worstRating: "1",
      ratingCount: ratingCount,
    },
    hasCourseInstance: {
      "@type": "CourseInstance",
      courseMode: "online",
      courseWorkload: "PT10H",
      offers: {
        "@type": "Offer",
        availability: "https://schema.org/InStock",
        price: "0",
        priceCurrency: "INR",
      },
    },
    review: reviews.map((r) => ({
      "@type": "Review",
      author: {
        "@type": "Person",
        name: r.author,
      },
      datePublished: r.datePublished,
      reviewBody: r.reviewBody,
      reviewRating: {
        "@type": "Rating",
        ratingValue: r.ratingValue,
        bestRating: "5",
      },
    })),
  };
}

/**
 * Generates Course schema for educational courses
 * @param name - The name of the course
 * @param description - Description of the course
 * @param courseCode - Optional course code
 * @param provider - Optional provider object or name string (defaults to SITE_CONFIG.name)
 */
export function generateCourseSchema(
  {
    name,
    description,
    courseCode = "",
    provider = SITE_CONFIG.name,
  }: CourseSchemaParams = {
    name: "NEET Test Series 2025",
    description:
      "Comprehensive test series for NEET 2025 preparation with mock tests, subject-wise tests, and detailed solutions.",
    courseCode: "NEET-TS-2025",
    provider: SITE_CONFIG.name,
  },
) {
  // Handle provider as either string or object
  const providerObject =
    typeof provider === "string"
      ? {
          "@type": "EducationalOrganization",
          name: provider,
          sameAs: SITE_DOMAIN,
        }
      : provider;

  return {
    "@context": "https://schema.org",
    "@type": "Course",
    name: name,
    description: description,
    courseCode: courseCode,
    provider: providerObject,
    audience: {
      "@type": "Audience",
      audienceType: "NEET Aspirants",
    },
    hasCourseInstance: {
      "@type": "CourseInstance",
      courseMode: "online",
      courseWorkload: "PT10H",
      offers: {
        "@type": "Offer",
        availability: "https://schema.org/InStock",
        price: "0",
        priceCurrency: "INR",
      },
    },
    educationalCredentialAwarded: "NEET Preparation Certificate",
    educationalLevel: "Undergraduate",
    learningResourceType: "Test Series",
    teaches: "NEET Exam Preparation",
  };
}

/**
 * Generates FAQ schema for structured data
 * @param faqs - Array of FAQ items with question and answer properties
 */
export function generateFaqSchema(
  faqs: Array<{ question: string; answer: string }>,
) {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: faqs.map((faq) => ({
      "@type": "Question",
      name: faq.question,
      acceptedAnswer: {
        "@type": "Answer",
        text: faq.answer,
      },
    })),
  };
}

/**
 * Generates a schema for educational services with location targeting
 * This helps with local SEO for specific areas in Bangalore
 */
export function generateEducationalServiceSchema() {
  // Define service areas in Bangalore for targeting
  const bangaloreAreas = [
    "Yelahanka",
    "North Bangalore",
    "Hebbal",
    "Sahakarnagar",
    "RT Nagar",
    "Jakkur",
    "Devanahalli",
    "Bagalur",
  ];

  // Define our main educational services
  const educationalServices: EducationalService[] = [
    {
      name: "NEET Coaching for PU Students",
      url: "/courses/neet-for-pu-students",
      description:
        "Comprehensive NEET preparation program for PU students with expert faculty and personalized mentoring",
    },
    {
      name: "NEET Long Term Coaching for Repeaters",
      url: "/courses/long-term-for-neet-repeaters",
      description:
        "Specialized NEET coaching program for repeaters with proven strategies for score improvement",
    },
    {
      name: "JEE Coaching for PU Students",
      url: "/courses/jee-for-pu-students",
      description:
        "Expert JEE preparation program for PU students with focus on problem-solving techniques",
    },
    {
      name: "KCET Coaching for PU Students",
      url: "/courses/kcet-for-pu-students",
      description:
        "Comprehensive KCET preparation program for PU students with regular mock tests",
    },
  ];

  // Create service offerings for each area and service combination
  const serviceOfferings: ServiceSchema[] = [];

  // Add general service offerings
  educationalServices.forEach((service) => {
    serviceOfferings.push({
      "@type": "Service",
      name: service.name,
      url: `${SITE_DOMAIN}${service.url}`,
      description: service.description,
      provider: {
        "@type": "EducationalOrganization",
        name: SITE_CONFIG.name,
        sameAs: SITE_DOMAIN,
      },
      areaServed: {
        "@type": "City",
        name: "Bangalore",
      },
    });
  });

  // Add area-specific service offerings for NEET (most important service)
  bangaloreAreas.forEach((area) => {
    serviceOfferings.push({
      "@type": "Service",
      name: `NEET Coaching in ${area}`,
      url: `${SITE_DOMAIN}/courses/neet-for-pu-students`,
      description: `Top NEET coaching services in ${area}, Bangalore with expert faculty and proven results`,
      provider: {
        "@type": "EducationalOrganization",
        name: SITE_CONFIG.name,
        sameAs: SITE_DOMAIN,
      },
      areaServed: {
        "@type": "Place",
        name: `${area}, Bangalore`,
      },
    });
  });

  return {
    "@context": "https://schema.org",
    "@type": "EducationalOrganization",
    name: SITE_CONFIG.name,
    url: SITE_DOMAIN,
    logo: `${SITE_DOMAIN}/logo.png`,
    description:
      "Top NEET Long Term Coaching & PU Integrated Coaching Center in Bangalore. Expert faculty for NEET, JEE & KCET preparation.",
    hasOfferCatalog: {
      "@type": "OfferCatalog",
      name: "Educational Services",
      itemListElement: serviceOfferings.map((service) => ({
        "@type": "Offer",
        itemOffered: service,
      })),
    },
    address: {
      "@type": "PostalAddress",
      streetAddress: "Survey No: 92/5, RTO Office Road",
      addressLocality: "Singanayakanahalli, Yelahanka",
      addressRegion: "Karnataka",
      postalCode: "560064",
      addressCountry: "IN",
    },
    telephone: "+919008266404",
    sameAs: [
      SITE_CONFIG.social.facebook,
      SITE_CONFIG.social.instagram,
      SITE_CONFIG.maps.url,
    ],
  };
}
