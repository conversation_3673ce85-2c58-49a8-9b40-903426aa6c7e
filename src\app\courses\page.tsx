import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Courses | Aims Academy",
  description:
    "Explore our range of courses at Aims Academy. We offer PCMB, PCMC, PU Courses, and specialized programs for competitive exams like NEET and KCET.",
  keywords: [
    "Courses",
    "PCMB",
    "PCMC",
    "PU Courses",
    "NEET Preparation",
    "KCET Coaching",
    "Aims Academy",
  ],
  openGraph: {
    title: "Courses | Aims Academy",
    description:
      "Discover our comprehensive range of courses at Aims Academy. Expert faculty, personalized mentoring, and proven results.",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Aims Academy Courses",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Courses | Aims Academy",
    description:
      "Explore our diverse range of courses at Aims Academy. Join us for academic excellence and competitive exam success.",
  },
};

const CoursesPage = () => {
  return (
    <article>
      {/* Visually hidden h1 for SEO and accessibility */}
      <h1 className="sr-only">
        Courses at Aims Academy - PCMB, PCMC, NEET, JEE, and KCET Programs
      </h1>
    </article>
  );
};

export default CoursesPage;
