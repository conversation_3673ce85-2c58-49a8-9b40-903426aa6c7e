import { type Metadata } from "next";
import { AnimatedSection } from "~/components/gallery-components/animated-section/AnimatedSection";
import { Timeline } from "~/components/gallery-components/timeline/Timeline";
import { TextGenerateEffect } from "~/components/ui/text-generate-effect";
import { timelineItems } from "~/constants/gallery";

export const metadata: Metadata = {
  title: "A Glimpse into Our Story | AIMS Academy",
  description:
    "Explore the moments that shaped AIMS Academy through our gallery. Discover the passion, dedication, and vision behind Bangalore’s trusted name for NEET and JEE coaching.",
  keywords: [
    "AIMS Academy gallery",
    "NEET coaching in Bangalore",
    "JEE coaching in Bangalore",
    "AIMS Academy photos",
    "coaching institute Bangalore",
  ],
  openGraph: {
    title: "A Glimpse into Our Story | AIMS Academy Bangalore",
    description:
      "Step into the story of AIMS Academy through our gallery. Witness the passion and dedication that make us a leader in NEET and JEE coaching in Bangalore.",
    url: `${process.env.SITE_DOMAIN}/gallery`,
    siteName: "AIMS Academy",

    locale: "en_IN",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "A Glimpse into Our Story | AIMS Academy Bangalore",
    description:
      "Explore the moments that shaped AIMS Academy through our gallery. Discover the passion and dedication behind our NEET and JEE coaching success.",
  },
};
const GalleryPage = () => {
  return (
    <section className="py-12">
      {/* Heading */}
      <AnimatedSection>
        <div className="mx-auto text-center">
          <h1 className="text-4xl font-bold text-gray-900 md:text-5xl">
            <TextGenerateEffect words="Gallery - A Glimpse into Our Story" />
          </h1>
          <p className="mt-4 text-lg text-gray-600 md:text-xl">
            These snapshots capture the dedication and energy that have made
            AIMS Academy a trusted name in NEET and JEE coaching in Bangalore
          </p>
        </div>
      </AnimatedSection>

      {/* Timeline */}
      <AnimatedSection delay={0.2}>
        <Timeline items={timelineItems} />
      </AnimatedSection>
    </section>
  );
};

export default GalleryPage;
