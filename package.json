{"name": "aims-academy", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "dev": "next dev --turbo -H 0.0.0.0 -p 3000", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "pages:build": "npx @cloudflare/next-on-pages"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@t3-oss/env-nextjs": "^0.13.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "geist": "^1.4.2", "hono": "^4.7.7", "lottie-react": "^2.4.1", "lucide-react": "^0.488.0", "mimetext": "^3.0.27", "mini-svg-data-uri": "^1.4.4", "next": "^15.4.5", "react": "^18.3.1", "react-device-detect": "^2.2.3", "react-dom": "^18.3.1", "react-photo-view": "^1.2.7", "react-tilt": "^1.0.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "use-local-storage": "^3.0.0", "zod": "^3.24.3"}, "devDependencies": {"@next/bundle-analyzer": "^15.4.5", "@next/third-parties": "^15.4.5", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/npm": "^12.0.2", "@types/eslint": "^8.56.10", "@types/node": "^22.14.1", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "autoprefixer": "^10.4.21", "eslint": "^8.57.0", "eslint-config-next": "^15.4.5", "jiti": "^2.4.2", "next-themes": "^0.4.6", "nextjs-toploader": "^3.8.16", "postcss": "^8.5.6", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.14", "semantic-release": "^24.2.7", "tailwindcss": "^3.4.3", "typescript": "^5.8.3"}, "release": {"branches": ["main", "next"]}, "ct3aMetadata": {"initVersion": "7.38.1"}, "prettier": {"plugins": ["prettier-plugin-tailwindcss"]}}